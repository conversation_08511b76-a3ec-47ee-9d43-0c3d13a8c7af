<?php
/**
 * Fix Admin Password Script
 * This script will update the admin user password to ensure login works
 */

require_once 'config/config.php';

echo "<h2>Fix Admin Password</h2>";

try {
    $pdo = getDbConnection();
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p>Admin user found. Current details:</p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Name: " . $admin['name'] . "</li>";
        echo "<li>Email: " . $admin['email'] . "</li>";
        echo "<li>Role: " . $admin['role'] . "</li>";
        echo "<li>Status: " . ($admin['status'] ? 'Active' : 'Inactive') . "</li>";
        echo "</ul>";
        
        // Generate new password hash
        $new_password = 'admin123';
        $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        echo "<p>Generating new password hash...</p>";
        echo "<p>New hash: " . $new_hash . "</p>";
        
        // Test the new hash
        if (password_verify($new_password, $new_hash)) {
            echo "<p style='color: green;'>✓ New hash verification successful</p>";
            
            // Update the password in database
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
            if ($stmt->execute([$new_hash, '<EMAIL>'])) {
                echo "<p style='color: green;'>✓ Password updated successfully!</p>";
                
                // Verify the update
                $stmt = $pdo->prepare("SELECT password FROM users WHERE email = ?");
                $stmt->execute(['<EMAIL>']);
                $updated_user = $stmt->fetch();
                
                if (password_verify($new_password, $updated_user['password'])) {
                    echo "<p style='color: green;'>✓ Password verification after update: SUCCESS</p>";
                    echo "<p style='color: green; font-weight: bold;'>Login should now work with:</p>";
                    echo "<p style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
                    echo "Email: <strong><EMAIL></strong><br>";
                    echo "Password: <strong>admin123</strong>";
                    echo "</p>";
                } else {
                    echo "<p style='color: red;'>✗ Password verification after update: FAILED</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ Failed to update password</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ New hash verification failed</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Admin user not found. Creating new admin user...</p>";
        
        // Create new admin user
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, ?)");
        
        if ($stmt->execute(['Admin', '<EMAIL>', $password_hash, 'admin', 1])) {
            echo "<p style='color: green;'>✓ Admin user created successfully!</p>";
            echo "<p style='color: green; font-weight: bold;'>You can now login with:</p>";
            echo "<p style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
            echo "Email: <strong><EMAIL></strong><br>";
            echo "Password: <strong>admin123</strong>";
            echo "</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin user</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>Try Login Now</a></p>";
echo "<p><a href='test_db.php'>Test Database</a></p>";
?>
