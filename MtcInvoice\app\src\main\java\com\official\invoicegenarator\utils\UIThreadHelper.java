package com.official.invoicegenarator.utils;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

/**
 * Utility class to handle UI operations from background threads safely
 * Prevents crashes when trying to show Toast messages or update UI from non-UI threads
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class UIThreadHelper {
    
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /**
     * Show Toast message safely from any thread
     * 
     * @param context The context to show the toast in
     * @param message The message to display
     * @param duration Toast.LENGTH_SHORT or Toast.LENGTH_LONG
     */
    public static void showToast(Context context, String message, int duration) {
        if (context == null || message == null) {
            return;
        }
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // Already on main thread
            Toast.makeText(context, message, duration).show();
        } else {
            // Post to main thread
            mainHandler.post(() -> {
                try {
                    Toast.makeText(context, message, duration).show();
                } catch (Exception e) {
                    // Silently handle any exceptions to prevent crashes
                    android.util.Log.w("UIThreadHelper", "Failed to show toast: " + e.getMessage());
                }
            });
        }
    }
    
    /**
     * Show short Toast message safely from any thread
     * 
     * @param context The context to show the toast in
     * @param message The message to display
     */
    public static void showToast(Context context, String message) {
        showToast(context, message, Toast.LENGTH_SHORT);
    }
    
    /**
     * Run a task on the main UI thread
     * 
     * @param runnable The task to run on the main thread
     */
    public static void runOnUiThread(Runnable runnable) {
        if (runnable == null) {
            return;
        }
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // Already on main thread
            runnable.run();
        } else {
            // Post to main thread
            mainHandler.post(runnable);
        }
    }
    
    /**
     * Run a task on the main UI thread if we have an Activity context
     * 
     * @param activity The activity to run the task on
     * @param runnable The task to run on the main thread
     */
    public static void runOnUiThread(Activity activity, Runnable runnable) {
        if (activity == null || runnable == null) {
            return;
        }
        
        if (activity.isFinishing() || activity.isDestroyed()) {
            // Activity is not in a valid state
            return;
        }
        
        activity.runOnUiThread(runnable);
    }
    
    /**
     * Post a task to run on the main thread after a delay
     * 
     * @param runnable The task to run
     * @param delayMillis The delay in milliseconds
     */
    public static void postDelayed(Runnable runnable, long delayMillis) {
        if (runnable != null) {
            mainHandler.postDelayed(runnable, delayMillis);
        }
    }
    
    /**
     * Check if we're currently on the main UI thread
     * 
     * @return true if on main thread, false otherwise
     */
    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
    
    /**
     * Remove any pending posts of Runnable from the message queue
     * 
     * @param runnable The runnable to remove
     */
    public static void removeCallbacks(Runnable runnable) {
        if (runnable != null) {
            mainHandler.removeCallbacks(runnable);
        }
    }
}
