<?php
/**
 * Database Connection Test and Login Debug
 */

require_once 'config/config.php';

echo "<h2>Database Connection Test and Login Debug</h2>";

// Test login simulation
if (isset($_GET['test_login'])) {
    echo "<h3>Testing Login Process</h3>";

    $email = '<EMAIL>';
    $password = 'admin123';

    try {
        $pdo = getDbConnection();

        // Find user by email
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 1");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        echo "<p>Query executed: SELECT * FROM users WHERE email = '$email' AND status = 1</p>";
        echo "<p>User found: " . ($user ? 'Yes' : 'No') . "</p>";

        if ($user) {
            echo "<p>User data:</p>";
            echo "<pre>" . print_r($user, true) . "</pre>";

            echo "<p>Testing password verification:</p>";
            echo "<p>Input password: '$password'</p>";
            echo "<p>Stored hash: " . $user['password'] . "</p>";

            if (password_verify($password, $user['password'])) {
                echo "<p style='color: green;'>✓ Password verification successful - Login should work!</p>";
            } else {
                echo "<p style='color: red;'>✗ Password verification failed</p>";

                // Test with different password hashing
                echo "<p>Testing different password hashes:</p>";
                $test_hash1 = password_hash('admin123', PASSWORD_DEFAULT);
                $test_hash2 = password_hash('admin123', PASSWORD_BCRYPT);
                echo "<p>New hash 1: $test_hash1</p>";
                echo "<p>New hash 2: $test_hash2</p>";
                echo "<p>Verify with new hash 1: " . (password_verify('admin123', $test_hash1) ? 'Yes' : 'No') . "</p>";
                echo "<p>Verify with new hash 2: " . (password_verify('admin123', $test_hash2) ? 'Yes' : 'No') . "</p>";
            }
        }

    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Login test error: " . $e->getMessage() . "</p>";
    }

    echo "<hr>";
}

try {
    // Test database connection
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Users table exists</p>";

        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $admin = $stmt->fetch();

        if ($admin) {
            echo "<p style='color: green;'>✓ Admin user exists</p>";
            echo "<p>Admin user details:</p>";
            echo "<ul>";
            echo "<li>ID: " . $admin['id'] . "</li>";
            echo "<li>Name: " . $admin['name'] . "</li>";
            echo "<li>Email: " . $admin['email'] . "</li>";
            echo "<li>Role: " . $admin['role'] . "</li>";
            echo "<li>Status: " . ($admin['status'] ? 'Active' : 'Inactive') . "</li>";
            echo "<li>Password Hash: " . substr($admin['password'], 0, 20) . "...</li>";
            echo "</ul>";

            // Test password verification
            if (password_verify('admin123', $admin['password'])) {
                echo "<p style='color: green;'>✓ Password verification successful</p>";
            } else {
                echo "<p style='color: red;'>✗ Password verification failed</p>";
                echo "<p style='color: orange;'>⚠ This might be why login is not working!</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Admin user not found</p>";
        }

        // Show all users
        $stmt = $pdo->query("SELECT id, name, email, role, status FROM users");
        $users = $stmt->fetchAll();
        echo "<p>All users in database:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Status</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['name'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . ($user['status'] ? 'Active' : 'Inactive') . "</td>";
            echo "</tr>";
        }
        echo "</table>";

    } else {
        echo "<p style='color: red;'>✗ Users table does not exist</p>";
        echo "<p>Available tables:</p>";
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll();
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>" . array_values($table)[0] . "</li>";
        }
        echo "</ul>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='?test_login=1'>Test Login Process</a></p>";
echo "<p><a href='login.php'>Back to Login</a></p>";
?>
